const mongoose = require('mongoose');

const NotInList = mongoose.Schema(
  {
    memberName: {
      type: String,
      default: '',
    },
    functionName: { type: String, default: '' },
    shift: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
      required: true,
    },
    project: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
      required: true,
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
      required: true,
    },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    updatedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true }
);

// Add indexes for better query performance
NotInList.index({ shift: 1, deletedAt: 1 });
NotInList.index({ account: 1, deletedAt: 1 });
NotInList.index({ shift: 1, account: 1, deletedAt: 1 });

module.exports = mongoose.model('not-in-list', NotInList);
