const mongoose = require('mongoose');

const teamMember = new mongoose.Schema(
  {
    member: {
      type: mongoose.Types.ObjectId,
      ref: 'member',
    },
    function: {
      type: mongoose.Types.ObjectId,
      ref: 'function',
    },
    shift: {
      type: mongoose.Types.ObjectId,
      ref: 'shift',
    },
    isWorking: {
      type: Boolean,
      default: false,
    },
    status: {
      type: Boolean,
      default: false,
    },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    updatedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
    updatedAt: {
      type: Date,
      default: null,
    },
  },
  { timestamps: false }
);

// Add pre-save middleware for new documents
teamMember.pre('save', function (next) {
  if (this.isNew) {
    // For new documents, updatedAt stays null
    this.updatedAt = null;
  } else {
    // For document updates via save(), set updatedAt
    this.updatedAt = new Date();
  }
  next();
});

// Add pre-hooks for update operations
const updateOperations = ['updateOne', 'updateMany', 'findOneAndUpdate', 'findByIdAndUpdate'];

updateOperations.forEach(method => {
  teamMember.pre(method, function (next) {
    this.set({ updatedAt: new Date() });
    next();
  });
});

// Add indexes for better query performance
teamMember.index({ shift: 1, deletedAt: 1 });
teamMember.index({ member: 1, deletedAt: 1 });
teamMember.index({ shift: 1, member: 1, deletedAt: 1 });

module.exports = mongoose.model('teammember', teamMember);
