const baseRoute = '/api';

module.exports = app => {
  app.use(`${baseRoute}/auths`, require('./auth.route'));
  app.use(`${baseRoute}/users`, require('./user.route'));
  app.use(`${baseRoute}/projects`, require('./project.route'));
  app.use(`${baseRoute}/locations`, require('./location.route'));
  app.use(`${baseRoute}/files`, require('./config.route'));
  app.use(`${baseRoute}/safety-cards`, require('./safety-card.route'));
  app.use(`${baseRoute}/form-builders`, require('./form-builder.route'));
  app.use(`${baseRoute}/accounts`, require('./account.route'));
  app.use(`${baseRoute}/severities`, require('./severity.route'));
  app.use(`${baseRoute}/likelihoods`, require('./likelihood.route'));
  app.use(`${baseRoute}/categories`, require('./category.route'));
  app.use(`${baseRoute}/types`, require('./type.route'));
  app.use(`${baseRoute}/members`, require('./member.route'));
  app.use(`${baseRoute}/functions`, require('./function.route'));
  app.use(`${baseRoute}/assets`, require('./asset.route'));
  app.use(`${baseRoute}/licences`, require('./licence.route'));
  app.use(`${baseRoute}/permissions`, require('./permission.route'));
  app.use(`${baseRoute}/account-licences`, require('./account-licence.route'));
  app.use(`${baseRoute}/project-strings`, require('./project-string.route'));
  app.use(`${baseRoute}/scopes`, require('./scope.route'));
  app.use(`${baseRoute}/activities`, require('./activity.route'));
  app.use(`${baseRoute}/sync`, require('./sync.route'));
  app.use(`${baseRoute}/licence`, require('./licence.route'));
  app.use(`${baseRoute}/permission`, require('./permission.route'));
  app.use(`${baseRoute}/account-licence`, require('./account-licence.route'));
  app.use(`${baseRoute}/project-string`, require('./project-string.route'));
  app.use(`${baseRoute}/scope`, require('./scope.route'));
  app.use(`${baseRoute}/activity`, require('./activity.route'));
  app.use(`${baseRoute}/member`, require('./member.route'));
  app.use(`${baseRoute}/function`, require('./function.route'));
  app.use(`${baseRoute}/asset`, require('./asset.route'));
  app.use(`${baseRoute}/files`, require('./file-upload.route'));
  app.use(`${baseRoute}/teams`, require('./team.route'));
  app.use(`${baseRoute}/shifts`, require('./shift.route'));
  app.use(`${baseRoute}/contractual-detail`, require('./contractual-detail.route'));
  app.use(`${baseRoute}/user-certificate`, require('./user-certificate.route'));
  app.use(`${baseRoute}/team-members`, require('./team-members.route'));
  app.use(`${baseRoute}/shifts/activities`, require('./shift-activity.route'));
  app.use(`${baseRoute}/report-types`, require('./report-type.route'));
  app.use(`${baseRoute}/feedback`, require('./feedback.route'));
  app.use(`${baseRoute}/question`, require('./question.route'));
  app.use(`${baseRoute}/role`, require('./role.route'));
  app.use(`${baseRoute}/role-agreement`, require('./role-agreement.route'));
  app.use(`${baseRoute}/warehouses`, require('./warehouse.route'));
  app.use(`${baseRoute}/equipment-categories`, require('./equipment-category.route'));
  app.use(`${baseRoute}/equipment-types`, require('./equipment-type.route'));
  app.use(`${baseRoute}/hs-codes`, require('./hs-code.route'));
  app.use(
    `${baseRoute}/equipment-certificate-types`,
    require('./equipment-certificate-type.route')
  );
  app.use(`${baseRoute}/equipments`, require('./equipment.route'));
  app.use(
    `${baseRoute}/equipment-image-cetrificates`,
    require('./equipment-image-certificate.route')
  );
  app.use(`${baseRoute}/equipment-warehouse`, require('./equipment-warehouse.route'));
  app.use(`${baseRoute}/equipment-units`, require('./equipment-unit.route'));
  app.use(`${baseRoute}/equipment-quantity-types`, require('./equipment-quantity-type.route'));
  app.use(`${baseRoute}/currency-units`, require('./currency-unit.route'));
  app.use(`${baseRoute}/certificate`, require('./certificate.route'));
  app.use(`${baseRoute}/certificate-type`, require('./certificate-type.route'));
  app.use(`${baseRoute}/upload-certificate`, require('./upload-certificate.route'));
  app.use(`${baseRoute}/profile-function`, require('./profile-function.route'));
  app.use(`${baseRoute}/equipment-orders`, require('./equipment-order.route'));
  app.use(`${baseRoute}/pm-order`, require('./pm-order.route'));
  app.use(`${baseRoute}/wm-order`, require('./wm-order.route'));
  app.use(`${baseRoute}/return-order`, require('./return-order.route'));
  app.use(`${baseRoute}/inventory-history`, require('./inventory-history.route'));
  app.use(`${baseRoute}/reports`, require('./manage-report.route'));
  app.use(`${baseRoute}/report/questions`, require('./report-question.route'));
  app.use(`${baseRoute}/report/users`, require('./user-report.route'));
  app.use(`${baseRoute}/report/user-answers`, require('./user-report-answer.route'));
  app.use(`${baseRoute}/toolbox-talk`, require('./toolbox-talk.route'));
  app.use(`${baseRoute}/pdf-template`, require('./pdf-template.route'));
  app.use(`${baseRoute}/project-documents`, require('./project-document.route'));
  app.use(`${baseRoute}/version-info`, require('./version-info.route'));
  app.use(`${baseRoute}/report-document`, require('./report-document.route'));
  app.use(`${baseRoute}/dpr`, require('./dpr.route'));
  app.use(`${baseRoute}/project-equipment-type`, require('./project-equipment-type.route'));
  app.use(`${baseRoute}/shopping-cart`, require('./shopping-cart.route'));
  app.use(`${baseRoute}/ce-norms`, require('./ce-norms.route'));
  app.use(`${baseRoute}/manage-sync-api`, require('./manage-sync-api.route'));
  app.use(`${baseRoute}/not-in-list`, require('./not-in-list.route'));
  app.use(`${baseRoute}/nationality`, require('./nationality.route'));
  app.use(`${baseRoute}/logbooks`, require('../routes/logbooks.route'));
  app.use(`${baseRoute}/gdpr`, require('../routes/gdpr.route'));

  // Version 2 routes
  app.use(`${baseRoute}/v2/equipment-orders`, require('./equipment-order.v2.route'));
  app.use(`${baseRoute}/v2/pm-order`, require('./pm-order.v2.route'));
  app.use(`${baseRoute}/v2/wm-order`, require('./wm-order.v2.route'));
  app.use(`${baseRoute}/v2/shifts`, require('./shift.v2.route'));
  app.use(`${baseRoute}/v2/sync`, require('./sync.v2.route'));
  app.use(`${baseRoute}/v2/team-members`, require('./team-members.v2.route'));
  app.use(`${baseRoute}/v2/project-documents`, require('./project-document.v2.route'));
  app.use(`${baseRoute}/v2/contractual-detail`, require('./contractual-detail.v2.route'));
  app.use(`${baseRoute}/v2/users`, require('./user.v2.route'));
  app.use(`${baseRoute}/v2/projects`, require('./project.v2.route'));
};
