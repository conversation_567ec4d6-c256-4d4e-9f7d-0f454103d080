/* models */
const Shift = require('../models/shift.model');
const {
  accountPipelineFields,
  projectPipelineFields,
  teamPipelineFields,
  userCreatedByPipelineFields,
  userUpdatedByPipelineFields,
  locationPipelineFields,
  toObjectId,
} = require('../utils/common.utils');

// utils
const aggregateComponentUtils = require('../utils/aggregate-component.utils');

/**
 * Create User
 *
 * @param {*} shift
 * @returns
 */
exports.createShift = async shift => {
  return await Shift.create(shift);
};

/**
 * Update By Id
 *
 * @param {*} id
 * @param {*} shift
 * @returns
 */
exports.updateShift = async (id, shift) => {
  return Shift.findByIdAndUpdate(id, { $set: shift }, { new: true }).populate([
    ...accountPipelineFields,
    ...projectPipelineFields,
    ...teamPipelineFields,
    ...userCreatedByPipelineFields,
    ...userUpdatedByPipelineFields,
  ]);
};

/**
 * Delete Project
 *
 * @param {*} id
 * @returns
 */
exports.deleteShift = async (id, deletedAt) => {
  return Shift.findByIdAndUpdate(id, { $set: deletedAt });
};

/**
 * Get Shift By id
 *
 * @param {*} id
 * @returns
 */
exports.getShiftById = async id => {
  return Shift.findOne(
    {
      $and: [{ _id: id }, { deletedAt: null }],
    },
    { createdBy: 0, updatedBy: 0, createdAt: 0, updatedAt: 0, __v: 0, deletedAt: 0 }
  ).populate([
    ...accountPipelineFields,
    ...projectPipelineFields,
    ...locationPipelineFields,
    ...teamPipelineFields,
  ]);
};

/**
 * Get All Shift (Optimized)
 *
 * @param {*} page
 * @param {*} perPage
 * @param {*} account
 * @returns
 */
exports.getAllShifts = async (filterData, account, page, perPage, user) => {
  let obj = {};

  if (filterData.id) {
    obj._id = toObjectId(filterData.id);
  } else {
    obj = {
      account: account,
      project: filterData.project,
      status: filterData.status ? { $eq: filterData.status } : '',
      deletedAt: { $eq: null },
    };

    // Add date filter if exists
    if (filterData.startDate) {
      obj.startDate = filterData.startDate;
    }
    Object.keys(obj).forEach(key => {
      if (obj[key] === '') delete obj[key];
    });
  }

  filterData.status === undefined && delete obj.status;
  filterData.project === undefined && delete obj.project;
  filterData.startDate === undefined && delete obj.startDate;
  if (user) obj.createdBy = user;
  // Use $facet to combine count and data queries for better performance
  const pipeline = [
    {
      $match: obj,
    },
    {
      $facet: {
        // Count pipeline
        totalCount: [
          {
            $count: 'count',
          },
        ],
        // Data pipeline
        data: [
          // Add team member count and notInList members only
          {
            $lookup: {
              from: 'teammembers',
              let: { shiftId: '$_id' },
              pipeline: [
                {
                  $match: {
                    $expr: {
                      $and: [{ $eq: ['$shift', '$$shiftId'] }, { $eq: ['$deletedAt', null] }],
                    },
                  },
                },
                {
                  $lookup: {
                    from: 'members',
                    let: { memberId: '$member', functionId: '$function' },
                    pipeline: [
                      {
                        $match: {
                          $expr: { $eq: ['$_id', '$$memberId'] },
                        },
                      },
                      {
                        $lookup: {
                          from: 'users',
                          let: { userId: '$user' },
                          pipeline: [
                            {
                              $match: {
                                $expr: { $eq: ['$_id', '$$userId'] },
                              },
                            },
                            {
                              $project: {
                                fullName: {
                                  $concat: [
                                    {
                                      $cond: {
                                        if: {
                                          $or: [
                                            { $eq: ['$callingName', null] },
                                            { $eq: ['$callingName', ''] },
                                          ],
                                        },
                                        then: '$firstName',
                                        else: '$callingName',
                                      },
                                    },
                                    ' ',
                                    '$lastName',
                                  ],
                                },
                              },
                            },
                          ],
                          as: 'userInfo',
                        },
                      },
                      {
                        $lookup: {
                          from: 'functions',
                          let: { funcId: '$$functionId' },
                          pipeline: [
                            {
                              $match: {
                                $expr: { $eq: ['$_id', '$$funcId'] },
                              },
                            },
                            {
                              $project: {
                                functionName: 1,
                              },
                            },
                          ],
                          as: 'functionInfo',
                        },
                      },
                      {
                        $project: {
                          user: 1,
                          userInfo: { $arrayElemAt: ['$userInfo', 0] },
                          functionInfo: { $arrayElemAt: ['$functionInfo', 0] },
                        },
                      },
                    ],
                    as: 'memberInfo',
                  },
                },
                {
                  $project: {
                    _id: 1,
                    member: 1,
                    function: 1,
                    memberInfo: { $arrayElemAt: ['$memberInfo', 0] },
                  },
                },
              ],
              as: 'teamMembers',
            },
          },
          // Get only notInList members for display
          {
            $lookup: {
              from: 'not-in-lists',
              let: { shiftId: '$_id' },
              pipeline: [
                {
                  $match: {
                    $expr: {
                      $and: [{ $eq: ['$shift', '$$shiftId'] }, { $eq: ['$deletedAt', null] }],
                    },
                  },
                },
                {
                  $project: {
                    _id: 1,
                    memberName: 1,
                    functionName: 1,
                    notInList: { $literal: true },
                  },
                },
              ],
              as: 'notInListMembers',
            },
          },
          // Create optimized allMembers array with only notInList members
          {
            $addFields: {
              allMembers: '$notInListMembers',
              teamMemberCount: { $size: '$teamMembers' },
              notInListCount: { $size: '$notInListMembers' },
              totalMemberCount: {
                $add: [{ $size: '$teamMembers' }, { $size: '$notInListMembers' }],
              },
            },
          },
          // Lookup essential related data
          {
            $lookup: {
              from: 'projects',
              localField: 'project',
              foreignField: '_id',
              pipeline: [
                {
                  $project: {
                    _id: 1,
                    title: 1,
                    standByTypes: 1,
                    isActive: 1,
                    defaultIdentifier: 1,
                  },
                },
              ],
              as: 'projects',
            },
          },
          {
            $lookup: {
              from: 'projects',
              localField: 'defaultProject',
              foreignField: '_id',
              pipeline: [
                {
                  $project: {
                    _id: 1,
                    title: 1,
                    standByTypes: 1,
                    isActive: 1,
                    defaultIdentifier: 1,
                  },
                },
              ],
              as: 'defaultProject',
            },
          },
          {
            $lookup: {
              from: 'teams',
              localField: 'team',
              foreignField: '_id',
              pipeline: [
                {
                  $project: {
                    _id: 1,
                    teamsWfmName: 1,
                  },
                },
              ],
              as: 'teams',
            },
          },
          {
            $lookup: {
              from: 'accounts',
              localField: 'account',
              foreignField: '_id',
              as: 'accounts',
            },
          },
          // Get shift activities count
          {
            $lookup: {
              from: 'shift-activities',
              let: { shiftId: '$_id' },
              pipeline: [
                {
                  $match: {
                    $expr: {
                      $and: [{ $eq: ['$shift', '$$shiftId'] }, { $eq: ['$deletedAt', null] }],
                    },
                  },
                },
                {
                  $count: 'count',
                },
              ],
              as: 'shiftActivitiesCount',
            },
          },
          // Get created by user info
          {
            $lookup: {
              from: 'users',
              localField: 'createdBy',
              foreignField: '_id',
              pipeline: [
                {
                  $project: {
                    _id: 1,
                    callingName: 1,
                    firstName: 1,
                    lastName: 1,
                    role: 1,
                  },
                },
                {
                  $lookup: {
                    from: 'roles',
                    localField: 'role',
                    foreignField: '_id',
                    pipeline: [
                      {
                        $project: {
                          _id: 1,
                          title: 1,
                        },
                      },
                    ],
                    as: 'role',
                  },
                },
                {
                  $unwind: {
                    path: '$role',
                    preserveNullAndEmptyArrays: true,
                  },
                },
              ],
              as: 'createdBy',
            },
          },
          // Get updated by user info
          {
            $lookup: {
              from: 'users',
              localField: 'updatedBy',
              foreignField: '_id',
              pipeline: [
                {
                  $project: {
                    _id: 1,
                    callingName: 1,
                    firstName: 1,
                    lastName: 1,
                    role: 1,
                  },
                },
                {
                  $lookup: {
                    from: 'roles',
                    localField: 'role',
                    foreignField: '_id',
                    pipeline: [
                      {
                        $project: {
                          _id: 1,
                          title: 1,
                        },
                      },
                    ],
                    as: 'role',
                  },
                },
                {
                  $unwind: {
                    path: '$role',
                    preserveNullAndEmptyArrays: true,
                  },
                },
              ],
              as: 'updatedBy',
            },
          },
          // Final projection
          {
            $project: {
              _id: 1,
              startDate: 1,
              endDate: 1,
              duration: 1,
              status: 1,
              createdAt: 1,
              createdBy: { $arrayElemAt: ['$createdBy', 0] },
              updatedBy: { $arrayElemAt: ['$updatedBy', 0] },
              projects: { $arrayElemAt: ['$projects', 0] },
              defaultProject: { $arrayElemAt: ['$defaultProject', 0] },
              teams: { $arrayElemAt: ['$teams', 0] },
              accounts: { $arrayElemAt: ['$accounts', 0] },
              shiftActivitiesCount: {
                $ifNull: [{ $arrayElemAt: ['$shiftActivitiesCount.count', 0] }, 0],
              },
              allMembers: 1,
              teamMemberCount: 1,
              notInListCount: 1,
              totalMemberCount: 1,
            },
          },
          // Sort the data
          { $sort: { startDate: -1, createdAt: -1 } },
          // Add pagination if needed
          ...(page !== '' && perPage !== ''
            ? [{ $skip: parseInt(page) * parseInt(perPage) }, { $limit: parseInt(perPage) }]
            : []),
        ],
      },
    },
  ];

  const [result] = await Shift.aggregate(pipeline);
  const shiftData = result.data || [];
  const shiftCount = result.totalCount.length > 0 ? result.totalCount[0].count : 0;

  return { shiftData, shiftCount };
};

/**
 * Get All Shifts Optimized for Listing (Only returns notInList members)
 *
 * @param {*} filterData
 * @param {*} account
 * @param {*} page
 * @param {*} perPage
 * @param {*} user
 * @returns
 */
exports.getAllShiftsOptimized = async (filterData, account, page, perPage, user) => {
  let obj = {};

  if (filterData.id) {
    obj._id = toObjectId(filterData.id);
  } else {
    obj = {
      account: account,
      project: filterData.project,
      status: filterData.status ? { $eq: filterData.status } : '',
      deletedAt: { $eq: null },
    };

    // Add date filter if exists
    if (filterData.startDate) {
      obj.startDate = filterData.startDate;
    }
    Object.keys(obj).forEach(key => {
      if (obj[key] === '') delete obj[key];
    });
  }

  filterData.status === undefined && delete obj.status;
  filterData.project === undefined && delete obj.project;
  filterData.startDate === undefined && delete obj.startDate;
  if (user) obj.createdBy = user;

  // Optimized pipeline for shift listing
  const pipeline = [
    {
      $match: obj,
    },
    {
      $facet: {
        // Count pipeline
        totalCount: [{ $count: 'count' }],
        // Data pipeline
        data: [
          // Get team member count only (no detailed member data)
          {
            $lookup: {
              from: 'teammembers',
              let: { shiftId: '$_id' },
              pipeline: [
                {
                  $match: {
                    $expr: {
                      $and: [{ $eq: ['$shift', '$$shiftId'] }, { $eq: ['$deletedAt', null] }],
                    },
                  },
                },
                { $count: 'count' },
              ],
              as: 'teamMemberCount',
            },
          },
          // Get only notInList members for hover display
          {
            $lookup: {
              from: 'not-in-lists',
              let: { shiftId: '$_id' },
              pipeline: [
                {
                  $match: {
                    $expr: {
                      $and: [{ $eq: ['$shift', '$$shiftId'] }, { $eq: ['$deletedAt', null] }],
                    },
                  },
                },
                {
                  $project: {
                    _id: 1,
                    memberId: '$_id',
                    userId: '$_id',
                    functionId: '$_id',
                    memberName: 1,
                    functionName: 1,
                    notInList: { $literal: true },
                  },
                },
              ],
              as: 'notInListMembers',
            },
          },
          // Lookup essential related data
          {
            $lookup: {
              from: 'projects',
              localField: 'project',
              foreignField: '_id',
              pipeline: [
                {
                  $project: {
                    _id: 1,
                    title: 1,
                    standByTypes: 1,
                    isActive: 1,
                    defaultIdentifier: 1,
                  },
                },
              ],
              as: 'projects',
            },
          },
          {
            $lookup: {
              from: 'projects',
              localField: 'defaultProject',
              foreignField: '_id',
              pipeline: [
                {
                  $project: {
                    _id: 1,
                    title: 1,
                    standByTypes: 1,
                    isActive: 1,
                    defaultIdentifier: 1,
                  },
                },
              ],
              as: 'defaultProject',
            },
          },
          {
            $lookup: {
              from: 'teams',
              localField: 'team',
              foreignField: '_id',
              pipeline: [
                {
                  $project: {
                    _id: 1,
                    teamsWfmName: 1,
                  },
                },
              ],
              as: 'teams',
            },
          },
          {
            $lookup: {
              from: 'accounts',
              localField: 'account',
              foreignField: '_id',
              as: 'accounts',
            },
          },
          // Get shift activities count efficiently
          {
            $lookup: {
              from: 'shift-activities',
              let: { shiftId: '$_id' },
              pipeline: [
                {
                  $match: {
                    $expr: {
                      $and: [{ $eq: ['$shift', '$$shiftId'] }, { $eq: ['$deletedAt', null] }],
                    },
                  },
                },
                { $count: 'count' },
              ],
              as: 'shiftActivitiesCount',
            },
          },
          // Get created by user info
          {
            $lookup: {
              from: 'users',
              localField: 'createdBy',
              foreignField: '_id',
              pipeline: [
                {
                  $project: {
                    _id: 1,
                    callingName: 1,
                    firstName: 1,
                    lastName: 1,
                    role: 1,
                  },
                },
                {
                  $lookup: {
                    from: 'roles',
                    localField: 'role',
                    foreignField: '_id',
                    pipeline: [{ $project: { _id: 1, title: 1 } }],
                    as: 'role',
                  },
                },
                {
                  $unwind: {
                    path: '$role',
                    preserveNullAndEmptyArrays: true,
                  },
                },
              ],
              as: 'createdBy',
            },
          },
          // Get updated by user info
          {
            $lookup: {
              from: 'users',
              localField: 'updatedBy',
              foreignField: '_id',
              pipeline: [
                {
                  $project: {
                    _id: 1,
                    callingName: 1,
                    firstName: 1,
                    lastName: 1,
                    role: 1,
                  },
                },
                {
                  $lookup: {
                    from: 'roles',
                    localField: 'role',
                    foreignField: '_id',
                    pipeline: [{ $project: { _id: 1, title: 1 } }],
                    as: 'role',
                  },
                },
                {
                  $unwind: {
                    path: '$role',
                    preserveNullAndEmptyArrays: true,
                  },
                },
              ],
              as: 'updatedBy',
            },
          },
          // Final projection with optimized data structure
          {
            $project: {
              _id: 1,
              startDate: 1,
              endDate: 1,
              duration: 1,
              status: 1,
              createdAt: 1,
              createdBy: { $arrayElemAt: ['$createdBy', 0] },
              updatedBy: { $arrayElemAt: ['$updatedBy', 0] },
              projects: { $arrayElemAt: ['$projects', 0] },
              defaultProject: { $arrayElemAt: ['$defaultProject', 0] },
              teams: { $arrayElemAt: ['$teams', 0] },
              accounts: { $arrayElemAt: ['$accounts', 0] },
              shiftActivitiesCount: {
                $ifNull: [{ $arrayElemAt: ['$shiftActivitiesCount.count', 0] }, 0],
              },
              // Only include notInList members as requested
              allMembers: '$notInListMembers',
              teamMemberCount: {
                $ifNull: [{ $arrayElemAt: ['$teamMemberCount.count', 0] }, 0],
              },
              notInListCount: { $size: '$notInListMembers' },
              totalMemberCount: {
                $add: [
                  { $ifNull: [{ $arrayElemAt: ['$teamMemberCount.count', 0] }, 0] },
                  { $size: '$notInListMembers' },
                ],
              },
            },
          },
          // Sort the data
          { $sort: { startDate: -1, createdAt: -1 } },
          // Add pagination if needed
          ...(page !== '' && perPage !== ''
            ? [{ $skip: parseInt(page) * parseInt(perPage) }, { $limit: parseInt(perPage) }]
            : []),
        ],
      },
    },
  ];

  const [result] = await Shift.aggregate(pipeline);
  const shiftData = result.data || [];
  const shiftCount = result.totalCount.length > 0 ? result.totalCount[0].count : 0;

  return { shiftData, shiftCount };
};

/**
 * Filter Shift
 *
 * @param {*} req
 * @param {*} filteredShift
 * @returns
 */
exports.filterShift = async (req, filteredShift, page, perPage) => {
  const { project, date, status } = req.query;
  // Filter tasks based on project, date, and status
  if (project !== 'all') {
    filteredShift = filteredShift.filter(shift => shift.projects[0]._id == project);
  }

  if (date) {
    let today = new Date();
    switch (date) {
      case 'today': {
        today.setHours(0, 0, 0, 0); // set the time to midnight
        filteredShift = filteredShift.filter(
          shift =>
            shift.createdAt > today &&
            shift.createdAt < new Date(today.getTime() + 24 * 60 * 60 * 1000)
        );
        break;
      }
      case 'this_week': {
        const startOfWeek = new Date(
          today.getFullYear(),
          today.getMonth(),
          today.getDate() - today.getDay()
        );
        const endOfWeek = new Date(
          today.getFullYear(),
          today.getMonth(),
          today.getDate() - today.getDay() + 6
        );
        filteredShift = filteredShift.filter(
          shift => shift.createdAt > startOfWeek && shift.createdAt < endOfWeek
        );
        break;
      }
      case 'this_month': {
        const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
        const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);
        filteredShift = filteredShift.filter(
          shift => shift.createdAt > startOfMonth && shift.createdAt < endOfMonth
        );
        break;
      }
      case 'last_month': {
        const startOfLastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
        const endOfLastMonth = new Date(today.getFullYear(), today.getMonth(), 0);
        filteredShift = filteredShift.filter(
          shift => shift.createdAt > startOfLastMonth && shift.createdAt < endOfLastMonth
        );
        break;
      }
      case 'this_year': {
        const startOfYear = new Date(today.getFullYear(), 0, 1);
        const endOfYear = new Date(today.getFullYear(), 11, 31);
        filteredShift = filteredShift.filter(
          shift => shift.createdAt > startOfYear && shift.createdAt < endOfYear
        );
        break;
      }
      default: {
        break;
      }
    }
  }

  if (status) {
    if (status !== 'all') {
      filteredShift = filteredShift.filter(
        shift => shift.status.toLowerCase() === status.toLowerCase()
      );
    }
  }
  if (page && perPage) {
    const pageNumber = parseInt(page) || 1;
    const pageSize = parseInt(perPage) || 10;
    const start = (pageNumber - 1) * pageSize;
    const end = pageNumber * pageSize;
    filteredShift = filteredShift.slice(start, end);
  }
  return filteredShift;
};

exports.getCreatedDateFilter = async requestData => {
  let curr = new Date();
  let fromDate = '';
  let toDate = '';
  if (requestData.created) {
    switch (requestData.created) {
      case 'today': {
        delete requestData.created;
        const todayDate = new Date(curr.setHours(0, 0, 0, 0));
        requestData.createdAt = {
          $gte: todayDate,
        };
        break;
      }
      case 'this_week': {
        delete requestData.created;
        const first = curr.getDate() - curr.getDay();
        const last = first + 6;
        fromDate = new Date(curr.setDate(first));
        toDate = new Date(curr.setDate(last));
        requestData.createdAt = { $gte: fromDate, $lte: toDate };
        break;
      }
      case 'this_month': {
        delete requestData.created;
        fromDate = new Date(curr.getFullYear(), curr.getMonth(), 1);
        toDate = new Date(curr.getFullYear(), curr.getMonth() + 1, 0);
        requestData.createdAt = { $gte: fromDate, $lte: toDate };
        break;
      }
      case 'this_year': {
        delete requestData.created;
        fromDate = new Date(curr.getFullYear(), 0, 1);
        toDate = new Date(curr.getFullYear() + 1, 0, 1);
        requestData.createdAt = { $gte: fromDate, $lte: toDate };
        break;
      }
      case 'last_month': {
        delete requestData.created;
        const prevMonthLastDate = new Date(curr.getFullYear(), curr.getMonth(), 0);
        const prevMonthFirstDate = new Date(curr.getFullYear(), curr.getMonth() - 1, 1);
        requestData.createdAt = {
          $gte: new Date(prevMonthFirstDate),
          $lte: new Date(prevMonthLastDate),
        };
        break;
      }
      default: {
        delete requestData.created;
        break;
      }
    }
  }
  return requestData;
};

/**
 * Delete All Project Shift
 *
 * @param {*} id
 * @param {*} deletedAt
 * @returns
 */
exports.deleteAllProjectShift = async (projectId, deletedAt) => {
  return await Shift.updateMany(
    { project: projectId },
    {
      $set: deletedAt,
    }
  );
};

/**
 * Get All Shift
 *
 * @returns
 */
exports.getShiftByProjectId = async filter => {
  return Shift.findOne({
    $and: [
      { account: filter.account },
      { project: filter.project },
      { deletedAt: filter.deletedAt },
    ],
  }).select('_id');
};

exports.getSyncShiftById = async id => {
  return Shift.findOne(
    {
      $and: [{ _id: id }],
    },
    { createdBy: 0, updatedBy: 0, createdAt: 0, updatedAt: 0, __v: 0, deletedAt: 0 }
  ).populate([
    ...accountPipelineFields,
    ...projectPipelineFields,
    ...locationPipelineFields,
    ...teamPipelineFields,
  ]);
};

/**
 * Get All Shift Count
 *
 * @param {*} filter
 */
exports.getAllShiftsCount = async filter => {
  const [result] = await Shift.aggregate([
    {
      $match: filter,
    },
    {
      $facet: {
        shiftCount: [
          {
            $group: {
              _id: '$status',
              status: { $first: '$status' },
              count: { $sum: 1 },
            },
          },
          {
            $project: {
              _id: 0,
              status: 1,
              count: 1,
            },
          },
        ],
        totalShiftCount: [
          {
            $count: 'totalShiftCount',
          },
        ],
      },
    },
  ]);
  const shiftCount = result.shiftCount;

  const totalShiftCount =
    result.totalShiftCount.length === 0 ? 0 : result.totalShiftCount[0].totalShiftCount;

  return {
    shiftCount,
    totalShiftCount,
  };
};
/**
 * Get Shift PDF Details
 *
 * @param {*} filter
 * @returns
 */
exports.getShiftPDFDetails = async filter => {
  let pipeline = [
    {
      $match: filter,
    },
    aggregateComponentUtils.aggregateLookup('project'),
    aggregateComponentUtils.aggregateUnwind('project'),
    aggregateComponentUtils.aggregateLookup('createdBy'),
    aggregateComponentUtils.aggregateUnwind('createdBy'),
    aggregateComponentUtils.aggregateLookup('updatedBy'),
    aggregateComponentUtils.aggregateUnwind('updatedBy'),
    aggregateComponentUtils.aggregateLookup('account'),
    aggregateComponentUtils.aggregateUnwind('account'),
    aggregateComponentUtils.aggregateLookup('team'),
    aggregateComponentUtils.aggregateUnwind('team'),
    aggregateComponentUtils.aggregateLookup('shift-activities'),
  ];

  return await Shift.aggregate(pipeline);
};

/**
 * Get All Shift
 *
 * @param {*} page
 * @param {*} perPage
 * @param {*} account
 * @returns
 */
exports.getAllShiftsForExcel = async (filterData, account) => {
  let obj = {};

  if (filterData.id) {
    obj._id = toObjectId(filterData.id);
  } else {
    obj = {
      account,
      project: filterData.project,
      status: filterData.status ? { $eq: filterData.status } : '',
      createdAt: filterData.createdAt ?? '',
      deletedAt: { $eq: null },
    };
    Object.keys(obj).forEach(key => {
      if (obj[key] === '') delete obj[key];
    });
  }

  const pipeline = [
    {
      $match: obj,
    },
    {
      $lookup: {
        from: 'teammembers',
        let: { cids: '$_id' },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [{ $eq: ['$shift', '$$cids'] }, { $eq: ['$deletedAt', null] }],
              },
            },
          },
          {
            $lookup: {
              from: 'members',
              let: { cids: '$member', fids: '$function' },
              pipeline: [
                {
                  $match: {
                    $expr: {
                      $and: [{ $eq: ['$_id', '$$cids'] }],
                    },
                  },
                },
                {
                  $project: {
                    _id: 1,
                    user: 1,
                    project: 1,
                  },
                },
                {
                  $lookup: {
                    from: 'users',
                    let: { cids: '$user' },
                    pipeline: [
                      {
                        $match: {
                          $expr: {
                            $and: [{ $eq: ['$_id', '$$cids'] }],
                          },
                        },
                      },
                      {
                        $project: {
                          _id: 0,
                          fullName: {
                            $concat: [
                              {
                                $cond: {
                                  if: {
                                    $or: [
                                      { $eq: ['$callingName', null] },
                                      { $eq: ['$callingName', ''] },
                                    ],
                                  },
                                  then: '$firstName',
                                  else: '$callingName',
                                },
                              },
                              ' ',
                              '$lastName',
                            ],
                          },
                        },
                      },
                    ],
                    as: 'users',
                  },
                },
                {
                  $lookup: {
                    from: 'functions',
                    let: { cids: '$function', fids: '$$fids' },
                    pipeline: [
                      {
                        $match: {
                          $expr: {
                            $and: [{ $eq: ['$_id', '$$fids'] }],
                          },
                        },
                      },
                      {
                        $project: {
                          _id: 0,
                          functionName: 1,
                        },
                      },
                    ],
                    as: 'functions',
                  },
                },
              ],
              as: 'members',
            },
          },
          {
            $project: {
              _id: 1,
              member: 1,
              function: 1,
              shift: 1,
              members: 1,
            },
          },
        ],
        as: 'teammembers',
      },
    },
    {
      $lookup: {
        from: 'not-in-lists',
        let: { cids: '$_id' },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [{ $eq: ['$shift', '$$cids'] }, { $eq: ['$deletedAt', null] }],
              },
            },
          },
          {
            $project: {
              _id: 1,
              member: '$memberName',
              function: '$functionName',
              shift: 1,
              members: {
                _id: '$_id',
                project: '$project',
                user: '$createdBy',
                users: [],
                functions: [],
                notInList: { $literal: true },
              },
            },
          },
        ],
        as: 'notInListMembers',
      },
    },
    {
      $addFields: {
        teammembers: {
          $concatArrays: ['$teammembers', '$notInListMembers'],
        },
      },
    },
    {
      $lookup: {
        from: 'projects',
        localField: 'project',
        pipeline: [
          {
            $project: {
              _id: 1,
              title: 1,
              standByTypes: 1,
              isActive: 1,
              defaultIdentifier: 1,
            },
          },
        ],
        foreignField: '_id',
        as: 'projects',
      },
    },
    {
      $match: {
        projects: {
          $elemMatch: {
            $exists: true,
          },
        },
      },
    },
    {
      $lookup: {
        from: 'projects',
        localField: 'defaultProject',
        pipeline: [
          {
            $project: {
              _id: 1,
              title: 1,
              standByTypes: 1,
              isActive: 1,
              defaultIdentifier: 1,
            },
          },
        ],
        foreignField: '_id',
        as: 'defaultProject',
      },
    },
    {
      $lookup: {
        from: 'teams',
        localField: 'team',
        foreignField: '_id',
        pipeline: [
          {
            $project: {
              _id: 1,
              teamsWfmName: 1,
            },
          },
        ],
        as: 'teams',
      },
    },
    {
      $match: {
        teams: {
          $elemMatch: {
            $exists: true,
          },
        },
      },
    },
    {
      $lookup: {
        from: 'accounts',
        localField: 'account',
        foreignField: '_id',
        as: 'accounts',
      },
    },
    {
      $match: {
        accounts: {
          $elemMatch: {
            $exists: true,
          },
        },
      },
    },
    {
      $lookup: {
        from: 'users',
        localField: 'createdBy',
        foreignField: '_id',
        pipeline: [
          {
            $project: {
              _id: 1,
              callingName: 1,
              firstName: 1,
              lastName: 1,
              role: 1,
            },
          },
          {
            $lookup: {
              from: 'roles',
              localField: 'role',
              foreignField: '_id',
              pipeline: [
                {
                  $project: {
                    _id: 1,
                    title: 1,
                  },
                },
              ],
              as: 'role',
            },
          },
        ],
        as: 'createdBy',
      },
    },
    {
      $lookup: {
        from: 'users',
        localField: 'updatedBy',
        foreignField: '_id',
        as: 'updatedBy',
        pipeline: [
          {
            $project: {
              _id: 1,
              callingName: 1,
              firstName: 1,
              lastName: 1,
              role: 1,
            },
          },
          {
            $lookup: {
              from: 'roles',
              localField: 'role',
              foreignField: '_id',
              pipeline: [
                {
                  $project: {
                    _id: 1,
                    title: 1,
                  },
                },
              ],
              as: 'role',
            },
          },
        ],
      },
    },
    {
      $unwind: {
        path: '$createdBy',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $unwind: {
        path: '$updatedBy',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $unwind: {
        path: '$teammembers',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $unwind: {
        path: '$defaultProject',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $unwind: {
        path: '$teammembers.members',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $unwind: {
        path: '$teammembers.members.users',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $unwind: {
        path: '$teammembers.members.functions',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $project: {
        _id: 1,
        startDate: 1,
        endDate: 1,
        duration: 1,
        status: 1,
        createdAt: 1,
        createdBy: 1,
        updatedBy: 1,
        teammembers: 1,
        projects: 1,
        defaultProject: 1,
        teams: 1,
        accounts: 1,
      },
    },
    { $sort: { startDate: -1 } },
  ];
  let shiftData = await Shift.aggregate(pipeline);

  return { shiftData };
};

exports.getShiftByFilter = async filter => {
  return await Shift.find(filter);
};

exports.getStartDateFilter = async requestData => {
  if (requestData.created) {
    switch (requestData.created) {
      case 'today': {
        delete requestData.created;

        const today = new Date();
        const yyyy = today.getFullYear();
        const mm = String(today.getMonth() + 1).padStart(2, '0');
        const dd = String(today.getDate()).padStart(2, '0');

        const todayDateStr = `${yyyy}-${mm}-${dd}`;

        requestData.startDate = { $regex: `^${todayDateStr}` };

        break;
      }
      case 'yesterday': {
        delete requestData.created;
        const today = new Date();

        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);

        const yyyy = yesterday.getFullYear();
        const mm = String(yesterday.getMonth() + 1).padStart(2, '0');
        const dd = String(yesterday.getDate()).padStart(2, '0');

        const yesterdayDateStr = `${yyyy}-${mm}-${dd}`;

        requestData.startDate = { $regex: `^${yesterdayDateStr}` };

        break;
      }
      case 'this_week': {
        delete requestData.created;
        const curr = new Date();
        const first = new Date(curr);

        first.setDate(curr.getDate() - curr.getDay());

        const last = new Date(curr);
        last.setDate(curr.getDate() + (6 - curr.getDay()));

        const formatDate = date => {
          const yyyy = date.getFullYear();
          const mm = String(date.getMonth() + 1).padStart(2, '0');
          const dd = String(date.getDate()).padStart(2, '0');
          return `${yyyy}-${mm}-${dd}`;
        };

        const fromDate = formatDate(first);
        const toDate = formatDate(last);

        requestData.startDate = {
          $gte: fromDate,
          $lte: toDate,
        };

        break;
      }
      case 'this_month': {
        delete requestData.created;

        const curr = new Date();
        const first = new Date(curr.getFullYear(), curr.getMonth(), 1);
        const last = new Date(curr.getFullYear(), curr.getMonth() + 1, 0);

        const formatDate = date => {
          const yyyy = date.getFullYear();
          const mm = String(date.getMonth() + 1).padStart(2, '0');
          const dd = String(date.getDate()).padStart(2, '0');
          return `${yyyy}-${mm}-${dd}`;
        };

        const fromDate = formatDate(first);
        const toDate = formatDate(last);

        requestData.startDate = {
          $gte: fromDate,
          $lte: toDate,
        };

        break;
      }
      case 'this_year': {
        delete requestData.created;
        const curr = new Date();
        const first = new Date(curr.getFullYear(), 0, 1);
        const last = new Date(curr.getFullYear(), 11, 31);

        const formatDate = date => {
          const yyyy = date.getFullYear();
          const mm = String(date.getMonth() + 1).padStart(2, '0');
          const dd = String(date.getDate()).padStart(2, '0');
          return `${yyyy}-${mm}-${dd}`;
        };

        const fromDate = formatDate(first);
        const toDate = formatDate(last);

        requestData.startDate = {
          $gte: fromDate,
          $lte: toDate,
        };

        break;
      }
      case 'last_month': {
        delete requestData.created;

        const curr = new Date();
        const prevMonthLastDate = new Date(curr.getFullYear(), curr.getMonth(), 0);
        const prevMonthFirstDate = new Date(curr.getFullYear(), curr.getMonth() - 1, 1);

        const formatDate = date => {
          const yyyy = date.getFullYear();
          const mm = String(date.getMonth() + 1).padStart(2, '0');
          const dd = String(date.getDate()).padStart(2, '0');
          return `${yyyy}-${mm}-${dd}`;
        };

        const fromDate = formatDate(prevMonthFirstDate);
        const toDate = formatDate(prevMonthLastDate);

        requestData.startDate = {
          $gte: fromDate,
          $lte: toDate,
        };

        break;
      }
      default: {
        delete requestData.created;
        break;
      }
    }
  }
  return requestData;
};
