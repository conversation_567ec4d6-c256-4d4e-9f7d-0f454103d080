const mongoose = require('mongoose');
const { DateTime } = require('luxon');
const fs = require('fs').promises;
const path = require('path');

// services
const projectService = require('../services/project.service');
const memberService = require('../services/member.service');

const cryptoRandomString = require('crypto-random-string');

/**
 * Remove unwanted paramters
 *
 * @param {*} params
 * @param {*} removeKeys
 * @returns
 */
exports.filterParamsModify = async (params, removeKeys = []) => {
  // remove the keys
  for (let propName in params) {
    if (removeKeys.includes(propName)) {
      delete params[propName];
    }
  }

  // remove blank or all elements
  Object.keys(params).forEach(k => {
    if (params[k] === undefined) {
      return;
    }
    if (params[k] === '' || params[k].toLowerCase() === 'all' || k === 'format') {
      delete params[k];
    }
  });
  return params;
};

/**
 * Common mathed for set the filter for date
 *
 * @param {*} requestData
 * @returns
 */
exports.getCreatedDateFilter = async requestData => {
  let curr = new Date();
  let fromDate = '';
  let toDate = '';
  switch (requestData.created) {
    case 'today': {
      delete requestData.created;
      let todayDate = new Date();
      todayDate.setUTCHours(0, 0, 0, 0);
      requestData.createdAt = { $gt: todayDate };
      break;
    }
    case 'yesterday': {
      delete requestData.created;
      const todayDate = new Date();
      const yesterdayDate = new Date(todayDate.getTime() - global.constant.DAY_CONVERTOR);
      todayDate.setUTCHours(0, 0, 0, 0);
      yesterdayDate.setUTCHours(0, 0, 0, 0);
      requestData.createdAt = { $gte: yesterdayDate, $lt: todayDate };
      break;
    }
    case 'this_week': {
      delete requestData.created;
      let first = curr.getDate() - curr.getDay();
      let last = first + 6;
      let startOfWeek = new Date(curr.getFullYear(), curr.getMonth(), first);
      let endOfWeek = new Date(curr.getFullYear(), curr.getMonth(), last);
      startOfWeek.setUTCHours(24, 0);
      endOfWeek.setUTCHours(24, 0);
      requestData.createdAt = { $gte: startOfWeek, $lte: endOfWeek };
      break;
    }
    case 'this_month': {
      delete requestData.created;
      fromDate = new Date(curr.getFullYear(), curr.getMonth(), 1);
      toDate = new Date(curr.getFullYear(), curr.getMonth() + 1, 1);
      fromDate.setUTCHours(24, 0);
      toDate.setUTCHours(24, 0);
      requestData.createdAt = { $gte: fromDate, $lte: toDate };
      break;
    }
    case 'this_year': {
      delete requestData.created;
      fromDate = new Date(curr.getFullYear(), 0, 1);
      toDate = new Date(curr.getFullYear() + 1, 0, 1);
      fromDate.setUTCHours(24, 0);
      toDate.setUTCHours(24, 0);
      requestData.createdAt = { $gte: fromDate, $lte: toDate };
      break;
    }
    case 'custom': {
      delete requestData.created;
      let fromDate = new Date(requestData.fromDate);
      let toDate = new Date(requestData.toDate);
      delete requestData.fromDate;
      delete requestData.toDate;

      fromDate = this.getStartOfDay(fromDate);
      toDate = this.getEndOfDay(toDate);

      requestData.createdAt = { $gte: fromDate, $lte: toDate };

      break;
    }
    // ToDo will remove once test it
    // case 'last_month': {
    //   console.log('last_month');
    //   delete requestData.created;

    //   const curr = new Date();
    //   const prevMonthLastDate = new Date(curr.getFullYear(), curr.getMonth(), 0);
    //   const prevMonthFirstDate = new Date(curr.getFullYear(), curr.getMonth() - 1, 1);

    //   const formatDate = date => {
    //     const yyyy = date.getFullYear();
    //     const mm = String(date.getMonth() + 1).padStart(2, '0');
    //     const dd = String(date.getDate()).padStart(2, '0');
    //     return `${yyyy}-${mm}-${dd}`;
    //   };

    //   const fromDate = formatDate(prevMonthFirstDate);
    //   const toDate = formatDate(prevMonthLastDate);

    //   requestData.createdAt = {
    //     $gte: fromDate,
    //     $lte: toDate,
    //   };

    //   break;
    // }
    case 'last_month': {
      delete requestData.created;
      fromDate = new Date(curr.getFullYear(), curr.getMonth() - 1, 1);
      toDate = new Date(curr.getFullYear(), curr.getMonth(), 0);
      fromDate.setUTCHours(24, 0);
      toDate.setUTCHours(24, 0);
      requestData.createdAt = { $gte: fromDate, $lte: toDate };
      break;
    }
    default: {
      delete requestData.created;
      break;
    }
  }

  return requestData;
};

exports.isValidId = id => {
  try {
    let isValid = this.toObjectId(id).toString();
    return isValid === id.toString();
  } catch (error) {
    return false;
  }
};

exports.validateId = (id, errorMessage) => {
  if (id && !this.isValidId(id)) {
    throw new Error(errorMessage);
  }
  return true;
};

exports.toObjectId = id => {
  return new mongoose.Types.ObjectId(`${id}`);
};

exports.generateOrderNumber = (length, type) => {
  let orderPrefixes = {
    return: global.constant.RETURN_ORDER_PRE_FIX,
    order: global.constant.ORDER_PRE_FIX,
    purchase: global.constant.PURCHASE_EQUIPMENT_PRE_FIX,
    cancel: global.constant.CANCEL_EQUIPMENT_PRE_FIX,
    sync: global.constant.SYNC_HASH_PRE_FIX,
  };
  let prefix = orderPrefixes[type];
  const randomString = cryptoRandomString({ length });
  return `${prefix}${randomString}`;
};

/**
 * Check collection exists
 *
 * @param {*} collectionName
 * @returns
 */
exports.checkCollectionExists = async collectionName => {
  const collections = await mongoose.connection.db.listCollections().toArray();
  return collections.some(collection => collection.name === collectionName);
};

/**
 * Capitalize the first letter of each word and replace underscores with spaces
 *
 * @param {*} string
 * @returns
 */
exports.alterStringFromRequestString = string => {
  // Replace underscores with spaces
  const stringWithSpaces = string.replace(/_/g, ' ');

  // Capitalize the first letter of each word
  const capitalizedString = stringWithSpaces
    .split(' ')
    .map(word => {
      return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
    })
    .join(' ');

  return capitalizedString;
};

/**
 * Convert string to case insensitive regex
 *
 * @param {*} string
 * @returns
 */
exports.convertToCaseInsensetive = async string => {
  return { $regex: string, $options: 'i' };
};

exports.accountPipelineFields = [
  {
    path: 'account account',
    select: { name: 1, _id: 1 },
    strictPopulate: false,
  },
];

exports.projectPipelineFields = [
  {
    path: 'project project',
    select: { title: 1, _id: 1, defaultIdentifier: 1 },
    strictPopulate: false,
  },
];

exports.locationPipelineFields = [
  {
    path: 'location location',
    select: { title: 1, _id: 1 },
    strictPopulate: false,
  },
];

exports.fromLocationPipelineFields = [
  {
    path: 'fromLocation',
    select: { title: 1, _id: 1, deletedAt: 1 },
    strictPopulate: false,
  },
];

exports.toLocationPipelineFields = [
  {
    path: 'toLocation',
    select: { title: 1, _id: 1, deletedAt: 1 },
    strictPopulate: false,
  },
];

exports.teamPipelineFields = [
  {
    path: 'team team',
    select: { teamsWfmName: 1, _id: 1 },
    strictPopulate: false,
  },
];
exports.stringPipelineFields = [
  {
    path: 'string',
    select: { name: 1, _id: 1 },
    strictPopulate: false,
  },
];

exports.userCreatedByPipelineFields = [
  {
    path: 'user createdBy',
    select: { firstName: 1, lastName: 1, _id: 1 },
    strictPopulate: false,
  },
];
exports.userUpdatedByPipelineFields = [
  {
    path: 'user updatedBy',
    select: { firstName: 1, lastName: 1, _id: 1 },
    strictPopulate: false,
  },
];

exports.userDeletedByFieldPipelineFields = [
  {
    path: 'users deletedBy',
    select: { firstName: 1, lastName: 1, _id: 1 },
    strictPopulate: false,
  },
];

exports.borderStyle = {
  top: { style: 'thin' },
  left: { style: 'thin' },
  bottom: { style: 'thin' },
  right: { style: 'thin' },
};

exports.shiftTableHeaders = [
  { header: 'Team', key: 'team', width: 30 },
  { header: 'Member', key: 'member', width: 30 },
  { header: 'Member Not In List', key: 'notInListMember', width: 30 },
  { header: 'Function', key: 'function', width: 30 },
  { header: 'Activity', key: 'activity', width: 30 },
  { header: 'Location', key: 'location', width: 30 },
  { header: 'Scope', key: 'scope', width: 15 },
  { header: 'Shift Start Date/Time', key: 'startDate', width: 30 },
  { header: 'Activity Start Date/Time', key: 'activityStartDate', width: 30 },
  { header: 'Activity End Date/Time', key: 'endDate', width: 30 },
  { header: 'Total Hours (HH:MM)', key: 'hours', width: 25 },
  { header: 'Activity Description', key: 'description', width: 30 },
  { header: 'Status', key: 'status', width: 20 },
];

/**
 * Shift Mapper
 *
 * @param {*} shift
 * @returns
 */
exports.shiftRowMapper = async shift => {
  return {
    team: shift?.teams?.[0]?.teamsWfmName || '',
    member:
      shift?.teammembers?.members?.users?.fullName ||
      shift?.teammembers?.notInListMember ||
      shift?.teammembers?.member ||
      '',
    notInListMember: shift?.teammembers.members.notInList ? 'Y' : 'N',
    function:
      shift?.teammembers?.members?.functions?.functionName || shift?.teammembers?.function || '',
    activity: shift?.shiftActivities?.activities?.[0]?.name || '',
    location: shift?.shiftActivities?.location?.title
      ? shift?.shiftActivities?.location?.title.charAt(0).toUpperCase() +
        shift?.shiftActivities?.location?.title.slice(1)
      : '',
    scope: shift?.shiftActivities?.activities?.[0]?.scopes?.[0]?.name || '',
    startDate: shift?.startDate ? await this.formatDateByHourAndMinutes(shift?.startDate) : '',
    activityStartDate: shift?.shiftActivities?.activityStartTime
      ? await this.formatDateByHourAndMinutes(shift?.shiftActivities?.activityStartTime)
      : shift?.startDate
      ? await this.formatDateByHourAndMinutes(shift?.startDate)
      : '',
    endDate: shift?.shiftActivities?.endTime
      ? await this.formatDateByHourAndMinutes(shift?.shiftActivities?.endTime)
      : '',
    hours: shift.shiftActivities?.duration?.hours
      ? await this.formatHoursAndMinutes(shift.shiftActivities?.duration?.hours)
      : '',
    description: shift?.shiftActivities?.comments || '',
    status: shift.status || ' ',
  };
};

/**
 * Get Count From Query
 *
 * @param {*} model
 * @param {*} filter
 * @param {*} responseData
 * @returns
 */
exports.getCountFromQuery = async (model, filter, responseData) => {
  const DBModel = require(`../models/${model}.model`);

  // Create a clean filter for counting that works with regex objects
  const countFilter = {};

  // Process each filter property
  for (const key in filter) {
    if (filter[key] instanceof RegExp) {
      // If it's a RegExp, convert to MongoDB compatible regex query
      countFilter[key] = {
        $regex: filter[key].source,
        $options: filter[key].flags,
      };
    } else if (key === '$and' && Array.isArray(filter[key])) {
      // Handle $and operator with array of conditions
      countFilter.$and = filter[key].map(condition => {
        const processedCondition = {};
        for (const condKey in condition) {
          if (condition[condKey] instanceof RegExp) {
            processedCondition[condKey] = {
              $regex: condition[condKey].source,
              $options: condition[condKey].flags,
            };
          } else {
            processedCondition[condKey] = condition[condKey];
          }
        }
        return processedCondition;
      });
    } else {
      // For non-regex values, use as is
      countFilter[key] = filter[key];
    }
  }

  const count = await DBModel.countDocuments(countFilter);
  responseData.allRecordsCount = count;
  return responseData;
};

exports.cellColor = {
  type: 'pattern',
  pattern: 'solid',
  fgColor: { argb: 'FF191A51' },
};

exports.cellTextColor = {
  color: { argb: 'FFFFFFFF' },
};

exports.alignment = { vertical: 'middle', horizontal: 'center' };

/**
 * Format file Name
 *
 * @param {*} title
 * @returns
 */
exports.formatFileName = async title => {
  return title.replace(/[\\?%*:|"<> ]/g, '_');
};

exports.personalDetailsMapping = {
  callingName: 'Usual First Name',
  firstName: 'First Name',
  lastName: 'Last Name',
  email: 'Email',
  contactNumber: 'Contact Number',
  address: 'Address',
  motherLanguage: 'Mother Language',
  prefAirportDeprt: 'Preferred Airport Departure',
  travelTimeToAirport: 'Travel Time to Airport',
  drivingLicence: 'Driving Licence',
  seamansBook: 'Seamans Book',
  shoeSize: 'Shoe Size',
  windaId: 'Winda ID',
  clothesSize: 'Clothes Size',
  street: 'Street',
  area: 'Area',
  zipCode: 'Zip Code',
  city: 'City',
  state: 'State',
  country: 'Country',
  secondaryPrefAirportDeprt: 'Second Airport Departure',
  travelTimeToSecondAirport: 'Travel Time to Second Airport',
  resourceNumber: 'Resource Number',
  nationality: 'Nationality',
};

exports.contractualDetailsMapping = {
  passport: 'Passport/ID ',
  passportIssueDate: 'Passport Issue Date',
  passportExpiryDate: 'Passport Expiry Date',
  secondaryPassport: 'Second Passport/ID',
  secondaryPassportIssueDate: 'Second Passport Issue Date',
  secondaryPassportExpiryDate: 'Second Passport Expiry Date',
  nationalIdentificationNumber: 'National Identification Number',
  employmentType: 'Employment Type',
  birthPlace: 'Birth Place',
  companyName: 'Company Name',
  companyRegistrationNumber: 'Company Registration Number',
  companyVATNumber: 'Company VAT Number',
  companyAddress: 'Company Address',
  bankName: 'Bank Name',
  accountHolderName: 'Account Holder Name',
  bankAccount: 'Bank Account',
  bicSwift: 'BIC/SWIFT',
  birthDate: 'Birth Date',
  healthInsurance: 'Health Insurance',
  healthInsuranceIssueDate: 'Health Insurance Issue Date',
  healthInsuranceExpiryDate: 'Health Insurance Expiry Date',
  liabilityInsurance: 'Liability Insurance',
  liabilityInsuranceIssueDate: 'Liability Insurance Issue Date',
  liabilityInsuranceExpiryDate: 'Liability Insurance Expiry Date',
};

exports.nextOfKinData = [
  'kinName',
  'relationship',
  'kinContactNumber',
  'kinStreet',
  'kinZip',
  'kinCity',
  'kinCountry',
];

exports.nextofKin = [
  'kin Name',
  'Relationship',
  'Contact Number',
  'Street',
  'Zip',
  'City',
  'Country',
];

/**
 * Get projects by status and add to filter
 *
 * @param {*} status
 * @param {*} account
 * @param {*} filter
 * @returns
 */
exports.filterProjectStatus = async (status, account, filter) => {
  if (status !== 'all') {
    status = status.split(',');
    const projectList = await projectService.getAllProjects({
      status: { $in: status },
      account,
      deletedAt: null,
    });

    filter = {
      ...filter,
      project: {
        $in: projectList.length > 0 ? projectList.map(project => project._id) : [],
      },
    };
  }
  return filter;
};

/**
 * Get assign project lists
 *
 * @param {*} isAssignAllProjects
 * @param {*} searchData
 * @returns
 */
exports.getAssignedProjectList = async (isAssignAllProjects, searchData) => {
  let projectList;

  if (isAssignAllProjects) {
    projectList = await projectService.getAllProjects(searchData);
  } else {
    projectList = await memberService.fetchAssignProjects(searchData);
  }
  return projectList;
};

/**
 * Apply project filtering for non-admin users
 *
 * @param {*} filter - The existing filter object
 * @param {*} userData - User data containing role and account information
 * @param {*} queryProject - The project query parameter
 * @returns {*} Updated filter object with project filtering applied
 */
exports.applyProjectFilterForNonAdminUsers = async (filter, userData, queryProject) => {
  // Only apply project filtering for non-admin users when queryProject is 'all'
  if (
    ![global.constant.ADMIN_ROLE, global.constant.SUPER_ADMIN_ROLE].includes(userData.role.title) &&
    queryProject === 'all'
  ) {
    // For users with all project access, get all projects for the account
    if (userData.role.isAssignAllProjects) {
      const allProjects = await projectService.getAllProjects({
        account: userData.account,
        deletedAt: null,
      });

      if (allProjects.length > 0) {
        filter.project = { $in: allProjects.map(project => project._id) };
      }
    } else {
      // For users without all project access, get only their assigned projects
      const searchData = {
        user: userData._id,
        account: userData.account,
        deletedAt: null,
      };

      const projectList = await memberService.fetchAssignProjects(searchData);

      if (projectList.length > 0) {
        filter.project = { $in: projectList };
      }
    }
  }

  return filter;
};

exports.inventoryTableHeaders = [
  { header: 'Equipment Category', key: 'equipmentCategory', width: 30 },
  { header: 'Equipment Type', key: 'equipmentType', width: 15 },
  { header: 'Product Number', key: 'productNumber', width: 15 },
  { header: 'Equipment Name', key: 'equipmentName', width: 30 },
  { header: 'Serial Number', key: 'serialNumber', width: 15 },
  { header: 'Location', key: 'location', width: 15 },
  { header: 'Available Stocks', key: 'availableStocks', width: 15 },
  { header: 'Certificate Expiry', key: 'certificateExpiry', width: 15 },
];

/**
 * Inventory Mapper
 *
 * @param {*} invenotry
 * @returns
 */
exports.inventoryRowMapper = inventory => {
  let certificateExpiryDate = null;

  // Filter out valid certificates with non-null `endDate`
  const validCertificates = Array.isArray(inventory?.certificateType)
    ? inventory.certificateType.filter(certificate => certificate.endDate !== null)
    : [];

  // Map the valid certificate end dates into Date objects
  const certificateExpiry = validCertificates?.map(certificate => new Date(certificate.endDate));

  // Get the current date
  const currentDate = new Date();

  // Filter dates that are different from the current date
  const filteredDates =
    certificateExpiry?.length > 0
      ? certificateExpiry.filter(expiryDate => expiryDate.getTime() !== currentDate.getTime())
      : [];

  // Sort the filtered dates in ascending order
  filteredDates.sort((a, b) => a - b);

  // If there are valid dates, use the earliest one
  if (filteredDates.length > 0) {
    certificateExpiryDate = filteredDates[0].toISOString();
  }

  return {
    equipmentCategory: inventory.equipmentType.equipmentCategory[0].name || '',
    equipmentType: inventory.equipmentType.type || '',
    productNumber: inventory.equipmentNumber || '',
    equipmentName: inventory.name || '',
    serialNumber: inventory.serialNumber || '',
    location:
      (inventory.equipmentCurrentLocation &&
        inventory.equipmentCurrentLocation.replace(/_/g, ' ')) ||
      '',
    availableStocks: inventory.quantity || 0,
    certificateExpiry: certificateExpiryDate || '',
  };
};

/**
 * DPR Table Headers
 */
exports.dprTableHeaders = [
  { header: 'DPR No.', key: 'dprNo', width: 15 },
  { header: 'Project', key: 'project', width: 15 },
  { header: 'Version', key: 'version', width: 30 },
  { header: 'Date', key: 'dprDate', width: 15 },
  { header: 'Status', key: 'status', width: 15 },
  { header: 'Created By', key: 'createdBy', width: 15 },
];

/**
 * Format Date to DD-MM-YYYY HH:mm
 *
 * @param {*} date
 * @returns
 */
exports.formatDateByHourAndMinutes = async date => {
  date = new Date(date);
  const padZero = num => (num < 10 ? '0' : '') + num;

  const day = padZero(date.getDate());
  const month = padZero(date.getMonth() + 1);
  const year = date.getFullYear();
  const hours = padZero(date.getHours());
  const minutes = padZero(date.getMinutes());

  return `${day}/${month}/${year} ${hours}:${minutes}`;
};
/**
 * Format hours and minutes
 *
 * @param {*} hour
 * @returns
 */
exports.formatHoursAndMinutes = async hour => {
  let hours;
  let minutes;
  if (hour) {
    [hours, minutes] = String(hour).split('.');
    minutes = Math.round(
      (Number(minutes || 0) / global.constant.EXCEL_TIME_DIVISOR.ROUND_OFF_VALUE) *
        global.constant.EXCEL_TIME_DIVISOR.MINUTES_VALUE
    );
  } else {
    hours = 0;
    minutes = 0;
  }

  // Parse as integers
  hours = parseInt(hours || 0, 10);
  minutes = parseInt(minutes || 0, 10);

  // For Excel export, we need to return the raw time value
  // This will be properly formatted in the exportExcel function
  return {
    hours,
    minutes,
    rawValue: `${hours}:${minutes}`,
    excelTimeValue:
      hours / global.constant.EXCEL_TIME_DIVISOR.HOURS +
      minutes / global.constant.EXCEL_TIME_DIVISOR.MINUTES,
  };
};

/**
 * Calculate Total Days Between Two Dates
 *
 * @param {*} fromDate
 * @param {*} toDate
 * @returns
 */
exports.calculateTotalDays = async (fromDate, toDate) => {
  if (!fromDate || !toDate) return 0;
  const from = new Date(fromDate);
  const to = new Date(toDate);
  const diffTime = to - from;

  return Math.max(Math.ceil(diffTime / global.constant.DAY_CONVERTOR), 0);
};

/**
 * Convert UTC date to local timezone and format as YYYY-MM-DD
 * @param {Date|string} utcDate
 * @returns {string} Formatted date string in YYYY-MM-DD format
 */
exports.convertToLocalAndFormat = utcDate => {
  const localDateString = new Date(utcDate).toString();
  const localDate = new Date(localDateString);

  const year = localDate.getFullYear();
  const month = String(localDate.getMonth() + 1).padStart(2, '0');
  const day = String(localDate.getDate()).padStart(2, '0');

  return `${year}-${month}-${day}`;
};

/**
 * Replace underscore with space
 *
 * @param {*} string
 * @returns
 */
exports.replaceUnderscoreWithSpace = string => {
  return string.replace(/_/g, ' ');
};

/**
 * Converts UTC time to Local time based on provided timezone
 * @param {string} date
 * @param {string} timezone
 * @param {string} format
 * @returns {DateTime} local timezone with format
 */
exports.convertUTCToLocalTimezone = (utcDateTime, timezone, format) => {
  const dt = DateTime.fromJSDate(utcDateTime);
  return dt.setZone(timezone).toFormat(format);
};

/**
 * Returns a new Date object set to the end of the given date (23:59:59.999)
 *
 * @param {Date|string} date
 * @returns {Date}
 */
exports.getEndOfDay = date => {
  const endOfDay = new Date(date);
  endOfDay.setHours(...global.constant.END_OF_DAY_HOURS);

  return endOfDay;
};

/**
 * Returns a new Date object set to the start of the given date (00:00:00.000)
 *
 * @param {Date|string} date
 * @returns {Date}
 */
exports.getStartOfDay = date => {
  const startOfDay = new Date(date);
  startOfDay.setHours(...global.constant.START_OF_DAY_HOURS);

  return startOfDay;
};

/**
 * Returns requested file
 *
 * @param {*} fileName
 * @param {*} res
 * @returns
 */
exports.getRequestedFile = async (filePath, fileName) => {
  const htmlFilePath = path.join(__dirname, filePath, fileName);
  return fs.readFile(htmlFilePath, 'utf8');
};
