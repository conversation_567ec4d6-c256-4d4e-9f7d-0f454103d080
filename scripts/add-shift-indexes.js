/**
 * Database Migration Script: Add Indexes for Shift Optimization
 * 
 * This script adds database indexes to improve the performance of shift listing queries.
 * Run this script once to create the necessary indexes.
 * 
 * Usage: node scripts/add-shift-indexes.js
 */

const mongoose = require('mongoose');
require('dotenv').config();

// Database connection
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || process.env.DB_URL, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Add indexes for better performance
const addIndexes = async () => {
  try {
    const db = mongoose.connection.db;
    
    console.log('🔄 Adding indexes for shift optimization...');
    
    // Shift collection indexes
    console.log('📊 Adding shift collection indexes...');
    await db.collection('shifts').createIndex({ account: 1, deletedAt: 1 });
    await db.collection('shifts').createIndex({ account: 1, project: 1, deletedAt: 1 });
    await db.collection('shifts').createIndex({ account: 1, status: 1, deletedAt: 1 });
    await db.collection('shifts').createIndex({ account: 1, startDate: -1, deletedAt: 1 });
    await db.collection('shifts').createIndex({ account: 1, createdAt: -1, deletedAt: 1 });
    await db.collection('shifts').createIndex({ account: 1, project: 1, status: 1, deletedAt: 1 });
    await db.collection('shifts').createIndex({ createdBy: 1, deletedAt: 1 });
    
    // Team members collection indexes
    console.log('👥 Adding teammembers collection indexes...');
    await db.collection('teammembers').createIndex({ shift: 1, deletedAt: 1 });
    await db.collection('teammembers').createIndex({ member: 1, deletedAt: 1 });
    await db.collection('teammembers').createIndex({ shift: 1, member: 1, deletedAt: 1 });
    
    // Not-in-list collection indexes
    console.log('📝 Adding not-in-lists collection indexes...');
    await db.collection('not-in-lists').createIndex({ shift: 1, deletedAt: 1 });
    await db.collection('not-in-lists').createIndex({ account: 1, deletedAt: 1 });
    await db.collection('not-in-lists').createIndex({ shift: 1, account: 1, deletedAt: 1 });
    
    // Members collection indexes
    console.log('🧑‍💼 Adding members collection indexes...');
    await db.collection('members').createIndex({ user: 1, deletedAt: 1 });
    await db.collection('members').createIndex({ project: 1, deletedAt: 1 });
    await db.collection('members').createIndex({ account: 1, deletedAt: 1 });
    await db.collection('members').createIndex({ user: 1, project: 1, deletedAt: 1 });
    
    // Shift activities collection indexes
    console.log('🎯 Adding shift-activities collection indexes...');
    await db.collection('shift-activities').createIndex({ shift: 1, deletedAt: 1 });
    await db.collection('shift-activities').createIndex({ account: 1, deletedAt: 1 });
    
    console.log('✅ All indexes created successfully!');
    
    // Display existing indexes for verification
    console.log('\n📋 Verifying indexes...');
    const shiftIndexes = await db.collection('shifts').indexes();
    console.log('Shift indexes:', shiftIndexes.map(idx => idx.name));
    
    const teammemberIndexes = await db.collection('teammembers').indexes();
    console.log('Teammember indexes:', teammemberIndexes.map(idx => idx.name));
    
    const notInListIndexes = await db.collection('not-in-lists').indexes();
    console.log('Not-in-list indexes:', notInListIndexes.map(idx => idx.name));
    
    const memberIndexes = await db.collection('members').indexes();
    console.log('Member indexes:', memberIndexes.map(idx => idx.name));
    
  } catch (error) {
    console.error('❌ Error creating indexes:', error);
    throw error;
  }
};

// Main execution
const main = async () => {
  try {
    await connectDB();
    await addIndexes();
    console.log('\n🎉 Migration completed successfully!');
    console.log('💡 The shift listing API should now be significantly faster.');
  } catch (error) {
    console.error('❌ Migration failed:', error);
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
    process.exit(0);
  }
};

// Run the migration
if (require.main === module) {
  main();
}

module.exports = { addIndexes };
