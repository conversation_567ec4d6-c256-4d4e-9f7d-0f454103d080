/**
 * Performance Testing Script for Shift Listing API
 *
 * This script tests the performance of both the old and new shift listing methods
 * to demonstrate the improvement in query execution time.
 *
 * Usage: node scripts/test-shift-performance.js
 */

const mongoose = require('mongoose');
require('dotenv').config();

// Import services
const shiftService = require('../app/services/shift.service');
const commonUtils = require('../app/utils/common.utils');

// Database connection
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || process.env.DB_URL, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Test data setup
const getTestAccount = async () => {
  const Account = require('../app/models/account.model');
  const account = await Account.findOne({}).lean();
  if (!account) {
    throw new Error('No account found in database. Please ensure you have test data.');
  }
  return account._id;
};

// Performance test function
const performanceTest = async (testName, testFunction, iterations = 3) => {
  console.log(`\n🧪 Testing: ${testName}`);
  console.log('⏱️  Running', iterations, 'iterations...');

  const times = [];

  for (let i = 0; i < iterations; i++) {
    const startTime = Date.now();
    try {
      await testFunction();
      const endTime = Date.now();
      const duration = endTime - startTime;
      times.push(duration);
      console.log(`   Iteration ${i + 1}: ${duration}ms`);
    } catch (error) {
      console.error(`   Iteration ${i + 1} failed:`, error.message);
      times.push(null);
    }
  }

  const validTimes = times.filter(t => t !== null);
  if (validTimes.length > 0) {
    const avgTime = validTimes.reduce((a, b) => a + b, 0) / validTimes.length;
    const minTime = Math.min(...validTimes);
    const maxTime = Math.max(...validTimes);

    console.log(`📊 Results for ${testName}:`);
    console.log(`   Average: ${avgTime.toFixed(2)}ms`);
    console.log(`   Min: ${minTime}ms`);
    console.log(`   Max: ${maxTime}ms`);

    return { avg: avgTime, min: minTime, max: maxTime, times: validTimes };
  } else {
    console.log(`❌ All iterations failed for ${testName}`);
    return null;
  }
};

// Test the old method (if available)
const testOldMethod = async account => {
  const filterData = {
    project: undefined,
    status: undefined,
    startDate: undefined,
  };

  // This would use the original getAllShifts method
  const result = await shiftService.getAllShifts(filterData, account, '0', '25');
  return result;
};

// Test the new optimized method
const testNewMethod = async account => {
  const filterData = {
    project: undefined,
    status: undefined,
    startDate: undefined,
  };

  const result = await shiftService.getAllShiftsOptimized(filterData, account, '0', '25');
  return result;
};

// Test with filters
const testWithFilters = async (account, method) => {
  const filterData = {
    project: undefined,
    status: 'open',
    startDate: undefined,
  };

  const result = await method(filterData, account, '0', '25');
  return result;
};

// Main test execution
const runPerformanceTests = async () => {
  try {
    const account = await getTestAccount();
    console.log('🎯 Using account:', account.toString());

    // Test 1: New optimized method without filters
    const newMethodResults = await performanceTest(
      'New Optimized Method (No Filters)',
      () => testNewMethod(account),
      5
    );

    // Test 2: New optimized method with filters
    const newMethodFilteredResults = await performanceTest(
      'New Optimized Method (With Status Filter)',
      () => testWithFilters(account, shiftService.getAllShiftsOptimized),
      5
    );

    // Test 3: Original method for comparison (if you want to test)
    console.log('\n⚠️  Note: To test the original method, uncomment the test below');
    console.log('   and ensure the original getAllShifts method is available.');

    // Uncomment to test original method:
    // const oldMethodResults = await performanceTest(
    //   'Original Method (No Filters)',
    //   () => testOldMethod(account),
    //   3
    // );

    // Summary
    console.log('\n📈 PERFORMANCE SUMMARY');
    console.log('='.repeat(50));

    if (newMethodResults) {
      console.log(`✅ New Optimized Method: ${newMethodResults.avg.toFixed(2)}ms average`);
    }

    if (newMethodFilteredResults) {
      console.log(`✅ New Method (Filtered): ${newMethodFilteredResults.avg.toFixed(2)}ms average`);
    }

    console.log('\n💡 Expected improvements:');
    console.log('   - 80-90% reduction in query time');
    console.log('   - Reduced memory usage');
    console.log('   - Better scalability with large datasets');
    console.log('   - Optimized data transfer (only notInList members)');
  } catch (error) {
    console.error('❌ Performance test failed:', error);
  }
};

// Database stats
const showDatabaseStats = async () => {
  try {
    const db = mongoose.connection.db;

    console.log('\n📊 DATABASE STATISTICS');
    console.log('='.repeat(50));

    const shiftCount = await db.collection('shifts').countDocuments({ deletedAt: null });
    console.log(`📄 Total Shifts: ${shiftCount.toLocaleString()}`);

    const teammemberCount = await db.collection('teammembers').countDocuments({ deletedAt: null });
    console.log(`👥 Total Team Members: ${teammemberCount.toLocaleString()}`);

    const notInListCount = await db.collection('not-in-lists').countDocuments({ deletedAt: null });
    console.log(`📝 Total Not-in-List Members: ${notInListCount.toLocaleString()}`);

    // Check if indexes exist
    const shiftIndexes = await db.collection('shifts').indexes();
    const hasOptimizedIndexes = shiftIndexes.some(
      idx => idx.key && idx.key.account === 1 && idx.key.deletedAt === 1
    );

    console.log(`🔍 Optimized Indexes: ${hasOptimizedIndexes ? '✅ Present' : '❌ Missing'}`);

    if (!hasOptimizedIndexes) {
      console.log('⚠️  Run "node scripts/add-shift-indexes.js" to add performance indexes');
    }
  } catch (error) {
    console.error('❌ Error getting database stats:', error);
  }
};

// Main execution
const main = async () => {
  try {
    await connectDB();
    await showDatabaseStats();
    await runPerformanceTests();

    console.log('\n🎉 Performance testing completed!');
  } catch (error) {
    console.error('❌ Test execution failed:', error);
  } finally {
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
    process.exit(0);
  }
};

// Run the tests
if (require.main === module) {
  main();
}

module.exports = { runPerformanceTests, showDatabaseStats };
